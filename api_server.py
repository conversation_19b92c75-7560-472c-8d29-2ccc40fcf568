#!/usr/bin/env python3
"""
Mock Ovation CXM API Server
Serves the demo dummy data through realistic API endpoints
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional, List
import json
import uvicorn
from datetime import datetime

app = FastAPI(title="Ovation CXM Mock API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Load dummy data (you'll save the JSON as 'demo_data.json')
with open('demo_data.json', 'r') as f:
    data = json.load(f)

@app.get("/")
async def root():
    return {"message": "Ovation CXM Mock API", "version": "1.0.0"}

# Metadata endpoints
@app.get("/issues/meta/statuses")
async def get_statuses():
    return data["metadata"]["statuses"]

@app.get("/issues/meta/categories")
async def get_categories():
    return data["metadata"]["categories"]

@app.get("/issues/meta/priorities")
async def get_priorities():
    return data["metadata"]["priorities"]

@app.get("/issues/meta/types")
async def get_types():
    return data["metadata"]["types"]

@app.get("/issues/meta/resolutions")
async def get_resolutions():
    return data["metadata"]["resolutions"]

# Core issue endpoints
@app.get("/issues/get")
async def list_issues(
    status: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    priority: Optional[str] = Query(None),
    customer_id: Optional[str] = Query(None),
    limit: Optional[int] = Query(100)
):
    """Get a collection of issues with optional filtering"""
    issues = data["issues"].copy()
    
    # Apply filters
    if status:
        issues = [i for i in issues if i["status"] == status]
    if category:
        issues = [i for i in issues if i["category"] == category]
    if priority:
        issues = [i for i in issues if i["priority"] == priority]
    if customer_id:
        issues = [i for i in issues if i["customer_id"] == customer_id]
    
    # Apply limit
    issues = issues[:limit]
    
    return {
        "count": len(issues),
        "issues": issues
    }

@app.get("/issues/get/{issue_id}")
async def get_issue(issue_id: str):
    """Get a specific issue by ID"""
    issue = next((i for i in data["issues"] if i["issue_id"] == issue_id), None)
    if not issue:
        raise HTTPException(status_code=404, detail="Issue not found")
    return issue

@app.get("/issues/get/by/{field_name}/{field_value}")
async def get_issue_by_field(field_name: str, field_value: str):
    """Get issues by custom field"""
    issues = [i for i in data["issues"] if str(i.get(field_name)) == field_value]
    return {
        "count": len(issues),
        "issues": issues
    }

@app.get("/issues/status_history/{issue_id}")
async def get_status_history(issue_id: str):
    """Get status change history for an issue"""
    history = [h for h in data["status_history"] if h["issue_id"] == issue_id]
    return {
        "issue_id": issue_id,
        "count": len(history),
        "history": history
    }

@app.get("/issues/chat/history/{issue_id}")
async def get_chat_history(issue_id: str):
    """Get chat history for an issue"""
    chats = [c for c in data["chat_history"] if c["issue_id"] == issue_id]
    return {
        "issue_id": issue_id,
        "count": len(chats),
        "conversations": chats
    }

@app.get("/issues/log/history/{issue_id}")
async def get_log_history(issue_id: str):
    """Get all issue changes/logs"""
    logs = [l for l in data["issue_logs"] if l["issue_id"] == issue_id]
    return {
        "issue_id": issue_id,
        "count": len(logs),
        "logs": logs
    }

@app.post("/issues/put")
async def create_or_update_issue(issue_data: dict):
    """Create or update an issue"""
    # In a real system, this would save to database
    # For demo, we'll just return the submitted data with a new ID
    if "issue_id" not in issue_data:
        issue_data["issue_id"] = f"ISS-2024-{len(data['issues']) + 100:03d}"
    
    issue_data["created_date"] = datetime.now().isoformat() + "Z"
    issue_data["updated_date"] = datetime.now().isoformat() + "Z"
    
    return {
        "message": "Issue created/updated successfully",
        "issue": issue_data
    }

@app.post("/issues/escalate/{issue_id}")
async def escalate_issue(issue_id: str, escalation_data: dict = None):
    """Escalate an issue"""
    issue = next((i for i in data["issues"] if i["issue_id"] == issue_id), None)
    if not issue:
        raise HTTPException(status_code=404, detail="Issue not found")
    
    return {
        "message": f"Issue {issue_id} escalated successfully",
        "issue_id": issue_id,
        "new_status": "escalated",
        "escalated_at": datetime.now().isoformat() + "Z"
    }

@app.post("/issues/resolve/{issue_id}")
async def resolve_issue(issue_id: str, resolution_data: dict = None):
    """Resolve an issue"""
    issue = next((i for i in data["issues"] if i["issue_id"] == issue_id), None)
    if not issue:
        raise HTTPException(status_code=404, detail="Issue not found")
    
    return {
        "message": f"Issue {issue_id} resolved successfully",
        "issue_id": issue_id,
        "new_status": "resolved",
        "resolved_at": datetime.now().isoformat() + "Z"
    }

# Custom demo endpoints for MCP tools
@app.get("/demo/weekend_escalations")
async def get_weekend_escalations():
    """Demo endpoint: Get weekend escalations"""
    escalated_issues = [
        issue for issue in data["issues"] 
        if issue["issue_id"] in data["weekend_escalations"]
    ]
    return {
        "count": len(escalated_issues),
        "escalations": escalated_issues,
        "total_contract_value": sum(
            next(c["contract_value"] for c in data["customers"] 
                 if c["customer_id"] == issue["customer_id"])
            for issue in escalated_issues
        )
    }

@app.get("/demo/customer_health/{customer_id}")
async def get_customer_health(customer_id: str):
    """Demo endpoint: Analyze customer health"""
    customer = next((c for c in data["customers"] if c["customer_id"] == customer_id), None)
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")
    
    customer_issues = [i for i in data["issues"] if i["customer_id"] == customer_id]
    escalated_count = len([i for i in customer_issues if i["status"] == "escalated"])
    
    # Check if customer is flagged as churn risk
    churn_risk = data["churn_risk_indicators"].get(customer_id, {})
    
    return {
        "customer": customer,
        "issue_count": len(customer_issues),
        "escalated_count": escalated_count,
        "churn_risk": churn_risk,
        "recent_issues": customer_issues[-5:]  # Last 5 issues
    }

@app.get("/demo/performance_metrics")
async def get_performance_metrics():
    """Demo endpoint: Overall performance metrics"""
    total_issues = len(data["issues"])
    escalated_issues = len([i for i in data["issues"] if i["status"] == "escalated"])
    resolved_issues = len([i for i in data["issues"] if i["status"] == "resolved"])
    
    return {
        "total_issues": total_issues,
        "escalated_count": escalated_issues,
        "escalation_rate": round((escalated_issues / total_issues) * 100, 1),
        "resolution_rate": round((resolved_issues / total_issues) * 100, 1),
        "avg_resolution_time": "2.3 days",  # Calculated from dummy data
        "customer_satisfaction": 4.2
    }

if __name__ == "__main__":
    print("🚀 Starting Ovation CXM Mock API Server...")
    print("📊 Demo endpoints available at:")
    print("   http://localhost:8000/demo/weekend_escalations")
    print("   http://localhost:8000/demo/customer_health/CUST-12345")
    print("   http://localhost:8000/demo/performance_metrics")
    print("📚 API docs at: http://localhost:8000/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)