#!/usr/bin/env python3
"""
Demo MCP Server for Ovation CXM using FastMCP
Provides AI tools for customer service analysis
"""

import httpx
from mcp.server.fastmcp import FastMCP
import json
import os
from datetime import datetime

# API Configuration
API_BASE_URL = os.getenv("OVATION_API_URL", "http://localhost:8000")

# Create the FastMCP server
mcp = FastMCP("ovation-demo")


@mcp.tool()
async def analyze_weekend_escalations() -> str:
    """Analyze weekend escalations and provide prioritized action plan for Monday morning"""
    try:
        async with httpx.AsyncClient() as client:
            # Get weekend escalations
            response = await client.get(f"{API_BASE_URL}/demo/weekend_escalations")
            if response.status_code != 200:
                return f"Error: {response.status_code}"
            
            escalation_data = response.json()
            escalations = escalation_data["escalations"]
            
            # Analyze each escalation for context
            analysis_results = []
            for issue in escalations:
                # Get customer health for each
                customer_response = await client.get(f"{API_BASE_URL}/demo/customer_health/{issue['customer_id']}")
                customer_health = customer_response.json() if customer_response.status_code == 200 else {}
                
                # Get chat history for sentiment
                chat_response = await client.get(f"{API_BASE_URL}/issues/chat/history/{issue['issue_id']}")
                chat_data = chat_response.json() if chat_response.status_code == 200 else {"conversations": []}
                
                analysis_results.append({
                    "issue": issue,
                    "customer_health": customer_health,
                    "recent_chats": chat_data["conversations"]
                })
            
            # Generate prioritized response
            return format_escalation_analysis(analysis_results, escalation_data["total_contract_value"])
    
    except Exception as e:
        return f"Error: {str(e)}"


@mcp.tool()
async def get_customer_full_context(customer: str) -> str:
    """Get complete customer context including issue history, patterns, and risk assessment
    
    Args:
        customer: Customer ID (e.g. CUST-99999 for John Smith) or Customer Name (e.g. John Smith)
    """
    try:
        async with httpx.AsyncClient() as client:
            # Get customer health
            # First, resolve customer name to customer_id if needed
            customer_id = await resolve_customer_id(client, customer)
            if not customer_id:
                return f"Customer not found: {customer_id}"
        
            response = await client.get(f"{API_BASE_URL}/demo/customer_health/{customer_id}")
            if response.status_code != 200:
                return f"Customer not found: {customer_id}"
            
            customer_data = response.json()
            
            # Get all issues for this customer
            issues_response = await client.get(f"{API_BASE_URL}/issues/get/by/customer_id/{customer_id}")
            issues_data = issues_response.json() if issues_response.status_code == 200 else {"issues": []}
            
            return format_customer_context(customer_data, issues_data["issues"])
    
    except Exception as e:
        return f"Error: {str(e)}"


@mcp.tool()
async def get_issue_context(issue_id: str) -> str:
    """Get complete context for a specific issue including customer history and recommendations
    
    Args:
        issue_id: Issue ID (e.g. ISS-2024-015)
    """
    try:
        async with httpx.AsyncClient() as client:
            # Get issue details
            issue_response = await client.get(f"{API_BASE_URL}/issues/get/{issue_id}")
            if issue_response.status_code != 200:
                return f"Issue not found: {issue_id}"
            
            issue = issue_response.json()
            
            # Get customer context
            customer_response = await client.get(f"{API_BASE_URL}/demo/customer_health/{issue['customer_id']}")
            customer_data = customer_response.json() if customer_response.status_code == 200 else {}
            
            # Get chat history
            chat_response = await client.get(f"{API_BASE_URL}/issues/chat/history/{issue_id}")
            chat_data = chat_response.json() if chat_response.status_code == 200 else {"conversations": []}
            
            # Get issue logs
            logs_response = await client.get(f"{API_BASE_URL}/issues/log/history/{issue_id}")
            logs_data = logs_response.json() if logs_response.status_code == 200 else {"logs": []}
            
            return format_issue_context(issue, customer_data, chat_data["conversations"], logs_data["logs"])
    
    except Exception as e:
        return f"Error: {str(e)}"


@mcp.tool()
async def generate_executive_summary() -> str:
    """Generate executive dashboard with performance metrics and key insights"""
    try:
        async with httpx.AsyncClient() as client:
            # Get performance metrics
            metrics_response = await client.get(f"{API_BASE_URL}/demo/performance_metrics")
            metrics = metrics_response.json() if metrics_response.status_code == 200 else {}
            
            # Get weekend escalations for context
            escalations_response = await client.get(f"{API_BASE_URL}/demo/weekend_escalations")
            escalations = escalations_response.json() if escalations_response.status_code == 200 else {}
            
            return format_executive_summary(metrics, escalations)
    
    except Exception as e:
        return f"Error: {str(e)}"


@mcp.tool()
async def analyze_churn_risk() -> str:
    """Identify customers at risk of churning based on escalation patterns and sentiment"""
    try:
        async with httpx.AsyncClient() as client:
            # Get all escalated issues
            escalated_response = await client.get(f"{API_BASE_URL}/issues/get?status=escalated")
            escalated_data = escalated_response.json() if escalated_response.status_code == 200 else {"issues": []}
            
            # Analyze churn risk for each customer
            churn_analysis = []
            for issue in escalated_data["issues"]:
                customer_response = await client.get(f"{API_BASE_URL}/demo/customer_health/{issue['customer_id']}")
                if customer_response.status_code == 200:
                    customer_data = customer_response.json()
                    if customer_data.get("churn_risk"):
                        churn_analysis.append({
                            "customer": customer_data["customer"],
                            "risk": customer_data["churn_risk"],
                            "current_issue": issue
                        })
            
            return format_churn_analysis(churn_analysis)
    
    except Exception as e:
        return f"Error: {str(e)}"

async def resolve_customer_id(client, customer_identifier):
    """Resolve customer name or ID to actual customer_id"""
    # If it looks like a customer ID (starts with CUST-), use it directly
    if customer_identifier.startswith("CUST-"):
        return customer_identifier
    
    # Otherwise, search for customer by name
    try:
        # Get all customers and find by name
        response = await client.get(f"{API_BASE_URL}/issues/get")
        if response.status_code == 200:
            issues_data = response.json()
            # Look through issues to find matching customer name
            for issue in issues_data.get("issues", []):
                if issue.get("customer_name", "").lower() == customer_identifier.lower():
                    return issue.get("customer_id")
        
        # Fallback: try to get customer data directly
        customers_response = await client.get(f"{API_BASE_URL}/customers")
        if customers_response.status_code == 200:
            customers_data = customers_response.json()
            for customer in customers_data.get("customers", []):
                if customer.get("name", "").lower() == customer_identifier.lower():
                    return customer.get("customer_id")
    
    except Exception:
        pass
    
    return None

def format_escalation_analysis(analysis_results, total_value):
    """Format the weekend escalation analysis"""
    output = "🚨 **WEEKEND ESCALATIONS ANALYSIS** 🚨\n\n"
    output += f"You have **{len(analysis_results)} critical escalations** requiring immediate action:\n\n"
    
    # Sort by priority (churn risk, business impact, urgency)
    priority_order = []
    for result in analysis_results:
        issue = result["issue"]
        customer_health = result["customer_health"]
        chats = result["recent_chats"]
        
        # Calculate priority score
        priority_score = 0
        if customer_health.get("churn_risk"):
            priority_score += 100  # Churn risk is highest priority
        if issue["priority"] == "critical":
            priority_score += 50
        if any("CEO" in chat.get("message", "") for chat in chats):
            priority_score += 30
        
        priority_order.append((priority_score, result))
    
    priority_order.sort(reverse=True, key=lambda x: x[0])
    
    for i, (score, result) in enumerate(priority_order, 1):
        issue = result["issue"]
        customer_health = result["customer_health"]
        chats = result["recent_chats"]
        
        output += f"**{i}. {issue['customer_name']} ({issue['issue_id']})**"
        
        if customer_health.get("churn_risk"):
            output += " - 🔥 **HIGHEST PRIORITY**"
        
        output += f"\n- Issue: {issue['title']}\n"
        
        if customer_health.get("churn_risk"):
            risk = customer_health["churn_risk"]
            output += f"- **Churn Risk**: {risk['risk_level'].upper()} (${customer_health['customer']['contract_value']:,} contract at risk)\n"
            for indicator in risk["indicators"]:
                output += f"  • {indicator}\n"
        
        # Add recent customer quotes
        frustrated_chats = [c for c in chats if "frustrat" in c.get("sentiment", "") or "threat" in c.get("sentiment", "")]
        if frustrated_chats:
            latest_chat = frustrated_chats[-1]
            output += f"- Customer Quote: \"{latest_chat['message']}\"\n"
        
        # Add recommended action
        if "TechCorp" in issue["customer_name"]:
            output += "- **Action**: Call TechCorp CEO directly - they mentioned alternative vendors\n"
        elif "DataFlow" in issue["customer_name"]:
            output += "- **Action**: Fast-track to billing team lead immediately\n"
        elif "CloudFirst" in issue["customer_name"]:
            output += "- **Action**: Assign senior engineer immediately, consider emergency fix\n"
        
        output += "\n"
    
    output += "**Recommended Priority Order:**\n"
    for i, (score, result) in enumerate(priority_order, 1):
        reason = ""
        if result["customer_health"].get("churn_risk"):
            reason = " (relationship + churn risk)"
        elif "demo" in result["issue"]["title"].lower():
            reason = " (immediate business impact)"
        elif "billing" in result["issue"]["category"]:
            reason = " (can likely resolve quickly)"
        
        output += f"{i}. {result['issue']['customer_name']}{reason}\n"
    
    output += f"\n**Total contract value at risk: ${total_value:,}**\n"
    output += "\n💡 **Additional Notes:**\n"
    output += "- All 3 are premium enterprise customers\n"
    output += "- Suggest CEO/executive communication for high churn risk customers\n"
    output += "- Consider implementing dedicated enterprise support process"
    
    return output


def format_customer_context(customer_data, all_issues):
    """Format complete customer context"""
    customer = customer_data["customer"]
    
    output = f"📋 **{customer['name'].upper()} - COMPLETE CUSTOMER CONTEXT**\n\n"
    
    # Current issue info
    current_issues = [i for i in all_issues if i["status"] in ["open", "in_progress", "escalated"]]
    if current_issues:
        current = current_issues[0]  # Most recent
        output += f"**Current Issue ({current['issue_id']}):**\n"
        output += f"- {current['title']}\n"
        output += f"- Status: {current['status'].title()} since {current['created_date'][:10]}\n\n"
    
    # Pattern analysis
    issue_count = len(all_issues)
    billing_issues = len([i for i in all_issues if i["category"] == "billing"])
    
    if customer["type"] == "individual" and issue_count > 2:
        output += f"🚩 **PATTERN ALERT - Unusual for Individual Customer:**\n"
        output += f"{customer['name']} has had **{issue_count} issues in 6 months** (most individual customers have 0-1):\n\n"
    else:
        output += f"**Issue History ({issue_count} total issues):**\n"
    
    # List recent issues
    for i, issue in enumerate(sorted(all_issues, key=lambda x: x["created_date"]), 1):
        status_emoji = "✅" if issue["status"] == "resolved" else "🔄"
        output += f"{i}. **{issue['issue_id']}** ({issue['created_date'][:7]}): {issue['title']} {status_emoji} {issue['status'].title()}\n"
    
    output += "\n"
    
    # Key insights
    output += "**Key Insights:**\n"
    if billing_issues > 1:
        output += f"- **{billing_issues} of {issue_count} issues are billing-related** (unusual pattern)\n"
    
    if customer_data.get("churn_risk"):
        risk = customer_data["churn_risk"]
        output += f"- **Churn Risk**: {risk['risk_level'].upper()} - {', '.join(risk['indicators'])}\n"
    
    # Check for escalation patterns
    escalated_count = customer_data.get("escalated_count", 0)
    if escalated_count > 0:
        output += f"- **{escalated_count} escalated issues** indicate frustration or complexity\n"
    
    # Recommendations
    output += "\n💡 **RECOMMENDED APPROACH:**\n"
    if customer["type"] == "individual" and issue_count > 2:
        output += "1. **Acknowledge the pattern**: \"I see this is your second billing issue and I understand your frustration\"\n"
        output += "2. **Offer proactive resolution**: Account credit + comprehensive account review\n"
        output += "3. **Escalate to senior support**: Individual customers rarely have this many issues\n"
        output += "4. **Follow-up plan**: Assign dedicated point of contact for future issues\n"
    elif customer_data.get("churn_risk"):
        output += "1. **Executive engagement**: CEO/VP level communication required\n"
        output += "2. **Immediate escalation**: Assign most senior technical resources\n"
        output += "3. **Process review**: Implement dedicated enterprise support\n"
        output += "4. **Retention strategy**: Consider contract renegotiation/incentives\n"
    else:
        output += "1. **Standard resolution**: Follow normal support process\n"
        output += "2. **Monitor closely**: Watch for pattern development\n"
        output += "3. **Document thoroughly**: Ensure good customer history\n"
    
    if customer["type"] == "individual":
        output += "\n**Scripts to Use:**\n"
        output += "- \"I've reviewed your account history and I can see why you're frustrated...\"\n"
        output += "- \"Let me fix this immediately AND ensure it doesn't happen again...\"\n"
        output += "- \"I'm arranging a senior support review of your account...\"\n"
    
    return output


def format_issue_context(issue, customer_data, chats, logs):
    """Format specific issue context with customer background"""
    output = f"🎯 **ISSUE CONTEXT: {issue['issue_id']}**\n\n"
    
    output += f"**Issue Details:**\n"
    output += f"- Customer: {issue['customer_name']}\n"
    output += f"- Title: {issue['title']}\n"
    output += f"- Status: {issue['status'].title()}\n"
    output += f"- Priority: {issue['priority'].title()}\n"
    output += f"- Category: {issue['category'].title()}\n"
    output += f"- Created: {issue['created_date'][:10]}\n"
    
    if customer_data:
        customer = customer_data.get("customer", {})
        output += f"- Contract Value: ${customer.get('contract_value', 0):,}\n"
        output += f"- Support Tier: {customer.get('support_tier', 'standard').title()}\n"
    
    output += "\n"
    
    # Recent customer communications
    if chats:
        output += "**Recent Customer Communications:**\n"
        for chat in chats[-3:]:  # Last 3 chats
            sentiment_emoji = "😡" if "frustrat" in chat.get("sentiment", "") else "💬"
            output += f"{sentiment_emoji} *{chat['timestamp'][:10]}*: \"{chat['message']}\"\n"
        output += "\n"
    
    # Agent notes/logs
    if logs:
        output += "**Agent Investigation Notes:**\n"
        for log in logs[-2:]:  # Last 2 logs
            output += f"- *{log['timestamp'][:10]}* ({log['agent_name']}): {log['details']}\n"
        output += "\n"
    
    # Customer risk assessment
    if customer_data and customer_data.get("churn_risk"):
        risk = customer_data["churn_risk"]
        output += f"⚠️ **CHURN RISK ALERT: {risk['risk_level'].upper()}**\n"
        for indicator in risk["indicators"]:
            output += f"- {indicator}\n"
        output += "\n"
    
    return output


def format_executive_summary(metrics, escalations):
    """Format executive dashboard summary"""
    output = "📊 **CUSTOMER SERVICE EXECUTIVE SUMMARY - JUNE 2024**\n\n"
    
    output += "## 🎯 **KEY METRICS**\n"
    output += f"- **Total Issues**: {metrics.get('total_issues', 0)} (↑15% vs May)\n"
    output += f"- **Average Resolution Time**: {metrics.get('avg_resolution_time', 'N/A')} (↓12% vs May) ✅\n"
    output += f"- **Escalation Rate**: {metrics.get('escalation_rate', 0)}% (↑3% vs May) ⚠️\n"
    output += f"- **Customer Satisfaction**: {metrics.get('customer_satisfaction', 0)}/5 (stable)\n\n"
    
    output += "## ⚡ **PERFORMANCE HIGHLIGHTS**\n"
    output += "✅ **Speed Improving**: Despite 15% more tickets, resolution time decreased 12%\n"
    output += "✅ **Quality Maintained**: Customer satisfaction stable at 4.2/5\n"
    output += "✅ **Team Efficiency**: Marcus Johnson resolved 23% more cases than average\n\n"
    
    output += "## 🚨 **AREAS OF CONCERN**\n\n"
    output += "**1. Enterprise Customer Escalations**\n"
    output += f"- {escalations.get('count', 0)} critical escalations this weekend from top-tier customers\n"
    output += "- TechCorp showing churn risk signals ($2.4M contract)\n"
    output += "- Pattern: Integration/technical issues escalating faster\n\n"
    
    output += "**2. Individual Customer Issues**\n"
    output += "- John Smith: 4 issues in 6 months (highly unusual)\n"
    output += "- Indicates potential systemic account management gaps\n\n"
    
    output += "**3. Billing System Issues**\n"
    output += "- 30% of escalations related to billing/account problems\n"
    output += "- Suggests need for billing system audit\n\n"
    
    output += "## 💰 **FINANCIAL IMPACT**\n"
    contract_value = escalations.get('total_contract_value', 0)
    output += f"- **At Risk**: ${contract_value:,} in enterprise contracts (weekend escalations)\n"
    output += "- **Opportunity**: Improved resolution time saving ~$45K/month in support costs\n\n"
    
    output += "## 🎯 **RECOMMENDATIONS**\n"
    output += "1. **Immediate**: CEO-level outreach to TechCorp (churn prevention)\n"
    output += "2. **Short-term**: Billing system audit and process review\n"
    output += "3. **Long-term**: Enhanced enterprise customer success program\n\n"
    
    output += "## 📈 **TREND ANALYSIS**\n"
    output += "- Technical issues increasing in complexity\n"
    output += "- Enterprise customers have higher expectations\n"
    output += "- Individual customer patterns worth monitoring\n"
    output += "- Overall efficiency improving despite volume growth\n\n"
    
    output += "**Bottom Line**: Team performing well under increased load, but enterprise relationship management needs immediate attention.\n"
    
    return output


def format_churn_analysis(churn_analysis):
    """Format churn risk analysis"""
    output = "🔥 **CHURN RISK ANALYSIS**\n\n"
    
    if not churn_analysis:
        output += "✅ **No immediate churn risks detected.**\n"
        output += "All escalated customers appear to be in normal risk ranges.\n"
        return output
    
    total_at_risk = sum(item["customer"]["contract_value"] for item in churn_analysis)
    output += f"⚠️ **{len(churn_analysis)} customers at HIGH CHURN RISK**\n"
    output += f"**Total contract value at risk: ${total_at_risk:,}**\n\n"
    
    for i, item in enumerate(churn_analysis, 1):
        customer = item["customer"]
        risk = item["risk"]
        current_issue = item["current_issue"]
        
        output += f"**{i}. {customer['name']} (${customer['contract_value']:,} contract)**\n"
        output += f"- Current Issue: {current_issue['title']}\n"
        output += f"- Risk Level: {risk['risk_level'].upper()}\n"
        output += "- Risk Indicators:\n"
        for indicator in risk["indicators"]:
            output += f"  • {indicator}\n"
        
        # Recommended actions
        output += "- **Immediate Actions Required:**\n"
        if "CEO" in " ".join(risk["indicators"]):
            output += f"  • Executive-level engagement (CEO to CEO)\n"
        output += f"  • Assign most senior technical resources\n"
        output += f"  • Daily status updates to customer\n"
        output += f"  • Consider contract renegotiation/incentives\n"
        
        output += "\n"
    
    output += "💡 **Overall Recommendations:**\n"
    output += "1. **Immediate escalation protocol** for all high-risk customers\n"
    output += "2. **Executive engagement** within 24 hours\n"
    output += "3. **Dedicated enterprise support** implementation\n"
    output += "4. **Weekly churn risk reviews** going forward\n"
    
    return output


if __name__ == "__main__":
    print("🚀 Starting Ovation CXM Demo MCP Server with FastMCP...")
    print("🔗 Connecting to API at:", API_BASE_URL)
    print("📡 Ready to receive MCP requests...")
    mcp.run()